'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const AmenitiesSection = () => {
  const [activeTab, setActiveTab] = useState(0);

  const amenities = [
    {
      title: 'Private Study Rooms',
      description: '24-Hour Study Center',
      content: 'Our study nooks and private study rooms are perfect for group projects, homework assignments, and pre-exam study sessions.',
      image: 'bg-blue-200'
    },
    {
      title: 'Outdoor Swimming Pool',
      description: 'Resort-Style Swimming Pool',
      content: 'Our outdoor swimming pool is a rejuvenating escape from the summer heat.',
      image: 'bg-blue-400'
    },
    {
      title: '24/7 Fitness Hub',
      description: '24/7 Fitness Hub',
      content: 'Our fully equipped fitness center contains free weights, strength training equipment, a yoga room, and Fitness On Demand.',
      image: 'bg-green-200'
    },
    {
      title: 'Basketball & Volleyball Courts',
      description: 'Basketball & Volleyball Courts',
      content: 'Challenge your friends to a friendly competition at our outdoor basketball and sand volleyball courts.',
      image: 'bg-orange-200'
    },
    {
      title: 'Package Lockers',
      description: 'Luxer One Package Lockers',
      content: 'Deliveries have never been so simple than with our community package lockers.',
      image: 'bg-purple-200'
    }
  ];

  return (
    <section id="amenities" className="py-20 px-4 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        {/* Tab Navigation */}
        <div className="flex flex-wrap justify-center mb-12 space-x-2 space-y-2 md:space-y-0">
          {amenities.map((amenity, index) => (
            <button
              key={index}
              onClick={() => setActiveTab(index)}
              className={`px-6 py-3 rounded-lg font-medium transition-colors ${
                activeTab === index
                  ? 'bg-red-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-100'
              }`}
            >
              {amenity.title}
            </button>
          ))}
        </div>

        {/* Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5 }}
            className="grid lg:grid-cols-2 gap-12 items-center"
          >
            {/* Image */}
            <div className={`h-96 rounded-lg ${amenities[activeTab].image} flex items-center justify-center`}>
              <span className="text-gray-700 text-lg">{amenities[activeTab].title}</span>
            </div>

            {/* Content */}
            <div>
              <h3 className="text-3xl font-bold text-gray-900 mb-4">
                {amenities[activeTab].description}
              </h3>
              
              <p className="text-lg text-gray-700 mb-8 leading-relaxed">
                {amenities[activeTab].content}
              </p>
              
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-red-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-red-700 transition-colors"
              >
                All Amenities
              </motion.button>
            </div>
          </motion.div>
        </AnimatePresence>
      </div>
    </section>
  );
};

export default AmenitiesSection;
