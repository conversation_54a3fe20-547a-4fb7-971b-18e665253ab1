/* [next]/internal/font/google/roboto_slab_5e76aecc.module.css [app-client] (css) */
@font-face {
  font-family: Roboto Slab;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/cc841f969fd728f7-s.f9c6a990.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Roboto Slab;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/5b8679bd09703cb6-s.b59603fc.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Roboto Slab;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/5cf85ae98292dc9d-s.097e1c2f.woff2") format("woff2");
  unicode-range: U+1F??;
}

@font-face {
  font-family: Roboto Slab;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/017fcba2013ed860-s.b948fab9.woff2") format("woff2");
  unicode-range: U+370-377, U+37A-37F, U+384-38A, U+38C, U+38E-3A1, U+3A3-3FF;
}

@font-face {
  font-family: Roboto Slab;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/ac5492c4881346b7-s.1bb56655.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Roboto Slab;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/79cb3a6dba9f18ed-s.b06f96fd.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Roboto Slab;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/09f114e0fd06a62e-s.p.e86245b0.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Roboto Slab Fallback;
  src: local(Times New Roman);
  ascent-override: 89.69%;
  descent-override: 23.2%;
  line-gap-override: 0.0%;
  size-adjust: 116.83%;
}

.roboto_slab_5e76aecc-module__vsAiKq__className {
  font-family: Roboto Slab, Roboto Slab Fallback;
  font-style: normal;
  font-weight: 700;
}

.roboto_slab_5e76aecc-module__vsAiKq__variable {
  --font-roboto-slab: "Roboto Slab", "Roboto Slab Fallback";
}

/*# sourceMappingURL=%5Bnext%5D_internal_font_google_roboto_slab_5e76aecc_module_css_e59ae46c._.single.css.map*/