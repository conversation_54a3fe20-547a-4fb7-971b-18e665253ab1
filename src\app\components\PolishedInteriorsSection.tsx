'use client';

import { motion } from 'framer-motion';

const PolishedInteriorsSection = () => {
  return (
    <section className="py-20 px-4 bg-white">
      <div className="max-w-7xl mx-auto">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Image Gallery */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="grid grid-cols-2 gap-4"
          >
            <div className="space-y-4">
              <div className="bg-gray-300 h-48 rounded-lg flex items-center justify-center">
                <span className="text-gray-600">Modern Kitchen</span>
              </div>
              <div className="bg-gray-300 h-32 rounded-lg flex items-center justify-center">
                <span className="text-gray-600">Bathroom</span>
              </div>
            </div>
            <div className="space-y-4 mt-8">
              <div className="bg-gray-300 h-32 rounded-lg flex items-center justify-center">
                <span className="text-gray-600">Living Space</span>
              </div>
              <div className="bg-gray-300 h-48 rounded-lg flex items-center justify-center">
                <span className="text-gray-600">Bedroom</span>
              </div>
            </div>
          </motion.div>
          
          {/* Content */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Polished & Streamlined Interiors
            </h2>
            
            <h3 className="text-2xl font-semibold text-red-600 mb-6">
              With Refined Finishes
            </h3>
            
            <p className="text-lg text-gray-700 mb-6 leading-relaxed">
              Student apartments are more than a place to live. They host late-night study sessions, roommate movie nights, and serve as the backdrop for some of the most defining years of your life. You deserve a space that can keep up with it all.
            </p>
            
            <p className="text-lg text-gray-700 mb-8 leading-relaxed">
              At The Monroe, students can call our luxurious student apartments near IU Bloomington home. Featuring wood-plank style flooring, large windows, and elegant gray cabinetry, these stunning units are designed to impress.
            </p>
            
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-red-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-red-700 transition-colors"
            >
              Schedule Tour
            </motion.button>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default PolishedInteriorsSection;
