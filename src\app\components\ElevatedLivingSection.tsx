'use client';

import { motion } from 'framer-motion';

const ElevatedLivingSection = () => {
  return (
    <section className="py-20 px-4 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Elevated Student Living
              <span className="block text-red-600">Starts Here</span>
            </h2>
            
            <p className="text-lg text-gray-700 mb-8 leading-relaxed">
              Combining comfort, convenience, and a touch of sophistication, <PERSON> Monroe redefines the off-campus housing near IU Bloomington experience. Here, students are situated only a short distance from grocery stores, restaurants, bus routes, and the Indiana University Bloomington campus. Residents also enjoy cozy study nooks, premier amenities, and furnished interiors, each equipped with chic stainless steel appliances and a full-size, in-unit washer and dryer.
            </p>
            
            <p className="text-lg text-gray-700 mb-8 leading-relaxed">
              Discover a community specially designed for your needs as a student. Schedule an in-person tour today for a closer look at The Monroe!
            </p>
            
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-red-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-red-700 transition-colors"
            >
              Schedule Tour
            </motion.button>
          </motion.div>
          
          {/* Image Gallery */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="grid grid-cols-2 gap-4"
          >
            <div className="space-y-4">
              <div className="bg-gray-300 h-48 rounded-lg flex items-center justify-center">
                <span className="text-gray-600">Apartment Interior</span>
              </div>
              <div className="bg-gray-300 h-32 rounded-lg flex items-center justify-center">
                <span className="text-gray-600">Kitchen</span>
              </div>
            </div>
            <div className="space-y-4 mt-8">
              <div className="bg-gray-300 h-32 rounded-lg flex items-center justify-center">
                <span className="text-gray-600">Living Room</span>
              </div>
              <div className="bg-gray-300 h-48 rounded-lg flex items-center justify-center">
                <span className="text-gray-600">Bedroom</span>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default ElevatedLivingSection;
