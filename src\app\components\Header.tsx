'use client';

import { useState } from 'react';
import { Menu, X, ChevronDown } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isLifeAtMonroeOpen, setIsLifeAtMonroeOpen] = useState(false);

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);
  const toggleLifeAtMonroe = () => setIsLifeAtMonroeOpen(!isLifeAtMonroeOpen);

  return (
    <>
      {/* Top Banner */}
      <div className="bg-red-600 text-white text-center py-2 px-4">
        <p className="text-sm font-medium">
          Refer a friend for Fall 2025 and receive $250
        </p>
      </div>

      {/* Main Header */}
      <header className="bg-white shadow-md relative z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <div className="flex-shrink-0">
              <div className="text-2xl font-bold text-gray-900">
                The Monroe
              </div>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex space-x-8">
              <a href="#floor-plans" className="text-gray-700 hover:text-gray-900 font-medium">
                Floor Plans
              </a>
              <a href="#gallery" className="text-gray-700 hover:text-gray-900 font-medium">
                Gallery
              </a>
              <a href="#amenities" className="text-gray-700 hover:text-gray-900 font-medium">
                Amenities
              </a>
              
              {/* Life At Monroe Dropdown */}
              <div className="relative">
                <button
                  onClick={toggleLifeAtMonroe}
                  className="flex items-center text-gray-700 hover:text-gray-900 font-medium"
                >
                  Life At Monroe
                  <ChevronDown className="ml-1 h-4 w-4" />
                </button>
                
                <AnimatePresence>
                  {isLifeAtMonroeOpen && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      className="absolute top-full left-0 mt-2 w-48 bg-white rounded-md shadow-lg py-2 z-50"
                    >
                      <a href="#faqs" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        FAQs
                      </a>
                      <a href="#international" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        International Students
                      </a>
                      <a href="#community" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        Community Central
                      </a>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>

              <a href="#neighborhood" className="text-gray-700 hover:text-gray-900 font-medium">
                Neighborhood
              </a>
              <a href="#contact" className="text-gray-700 hover:text-gray-900 font-medium">
                Contact Us
              </a>
              <a href="#residents" className="text-gray-700 hover:text-gray-900 font-medium">
                Residents
              </a>
            </nav>

            {/* Apply Now Button */}
            <div className="hidden lg:block">
              <button className="bg-red-600 text-white px-6 py-2 rounded-md font-medium hover:bg-red-700 transition-colors">
                Apply Now
              </button>
            </div>

            {/* Mobile menu button */}
            <div className="lg:hidden">
              <button
                onClick={toggleMenu}
                className="text-gray-700 hover:text-gray-900"
              >
                {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Navigation */}
        <AnimatePresence>
          {isMenuOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="lg:hidden bg-white border-t"
            >
              <div className="px-4 py-2 space-y-1">
                <a href="#floor-plans" className="block py-2 text-gray-700 hover:text-gray-900 font-medium">
                  Floor Plans
                </a>
                <a href="#gallery" className="block py-2 text-gray-700 hover:text-gray-900 font-medium">
                  Gallery
                </a>
                <a href="#amenities" className="block py-2 text-gray-700 hover:text-gray-900 font-medium">
                  Amenities
                </a>
                <a href="#faqs" className="block py-2 text-gray-700 hover:text-gray-900 font-medium">
                  FAQs
                </a>
                <a href="#international" className="block py-2 text-gray-700 hover:text-gray-900 font-medium">
                  International Students
                </a>
                <a href="#community" className="block py-2 text-gray-700 hover:text-gray-900 font-medium">
                  Community Central
                </a>
                <a href="#neighborhood" className="block py-2 text-gray-700 hover:text-gray-900 font-medium">
                  Neighborhood
                </a>
                <a href="#contact" className="block py-2 text-gray-700 hover:text-gray-900 font-medium">
                  Contact Us
                </a>
                <a href="#residents" className="block py-2 text-gray-700 hover:text-gray-900 font-medium">
                  Residents
                </a>
                <button className="w-full mt-4 bg-red-600 text-white px-6 py-2 rounded-md font-medium hover:bg-red-700 transition-colors">
                  Apply Now
                </button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </header>
    </>
  );
};

export default Header;
