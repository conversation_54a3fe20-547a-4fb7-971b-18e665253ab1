{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/crimson-crossing/src/app/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Menu, X, ChevronDown } from 'lucide-react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\nconst Header = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isLifeAtMonroeOpen, setIsLifeAtMonroeOpen] = useState(false);\n\n  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);\n  const toggleLifeAtMonroe = () => setIsLifeAtMonroeOpen(!isLifeAtMonroeOpen);\n\n  return (\n    <>\n      {/* Top Banner */}\n      <div className=\"bg-red-600 text-white text-center py-2 px-4\">\n        <p className=\"text-sm font-medium\">\n          Refer a friend for Fall 2025 and receive $250\n        </p>\n      </div>\n\n      {/* Main Header */}\n      <header className=\"bg-white shadow-md relative z-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            {/* Logo */}\n            <div className=\"flex-shrink-0\">\n              <div className=\"text-2xl font-bold text-gray-900\">\n                The Monroe\n              </div>\n            </div>\n\n            {/* Desktop Navigation */}\n            <nav className=\"hidden lg:flex space-x-8\">\n              <a href=\"#floor-plans\" className=\"text-gray-700 hover:text-gray-900 font-medium\">\n                Floor Plans\n              </a>\n              <a href=\"#gallery\" className=\"text-gray-700 hover:text-gray-900 font-medium\">\n                Gallery\n              </a>\n              <a href=\"#amenities\" className=\"text-gray-700 hover:text-gray-900 font-medium\">\n                Amenities\n              </a>\n              \n              {/* Life At Monroe Dropdown */}\n              <div className=\"relative\">\n                <button\n                  onClick={toggleLifeAtMonroe}\n                  className=\"flex items-center text-gray-700 hover:text-gray-900 font-medium\"\n                >\n                  Life At Monroe\n                  <ChevronDown className=\"ml-1 h-4 w-4\" />\n                </button>\n                \n                <AnimatePresence>\n                  {isLifeAtMonroeOpen && (\n                    <motion.div\n                      initial={{ opacity: 0, y: -10 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      exit={{ opacity: 0, y: -10 }}\n                      className=\"absolute top-full left-0 mt-2 w-48 bg-white rounded-md shadow-lg py-2 z-50\"\n                    >\n                      <a href=\"#faqs\" className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\">\n                        FAQs\n                      </a>\n                      <a href=\"#international\" className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\">\n                        International Students\n                      </a>\n                      <a href=\"#community\" className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\">\n                        Community Central\n                      </a>\n                    </motion.div>\n                  )}\n                </AnimatePresence>\n              </div>\n\n              <a href=\"#neighborhood\" className=\"text-gray-700 hover:text-gray-900 font-medium\">\n                Neighborhood\n              </a>\n              <a href=\"#contact\" className=\"text-gray-700 hover:text-gray-900 font-medium\">\n                Contact Us\n              </a>\n              <a href=\"#residents\" className=\"text-gray-700 hover:text-gray-900 font-medium\">\n                Residents\n              </a>\n            </nav>\n\n            {/* Apply Now Button */}\n            <div className=\"hidden lg:block\">\n              <button className=\"bg-red-600 text-white px-6 py-2 rounded-md font-medium hover:bg-red-700 transition-colors\">\n                Apply Now\n              </button>\n            </div>\n\n            {/* Mobile menu button */}\n            <div className=\"lg:hidden\">\n              <button\n                onClick={toggleMenu}\n                className=\"text-gray-700 hover:text-gray-900\"\n              >\n                {isMenuOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        <AnimatePresence>\n          {isMenuOpen && (\n            <motion.div\n              initial={{ opacity: 0, height: 0 }}\n              animate={{ opacity: 1, height: 'auto' }}\n              exit={{ opacity: 0, height: 0 }}\n              className=\"lg:hidden bg-white border-t\"\n            >\n              <div className=\"px-4 py-2 space-y-1\">\n                <a href=\"#floor-plans\" className=\"block py-2 text-gray-700 hover:text-gray-900 font-medium\">\n                  Floor Plans\n                </a>\n                <a href=\"#gallery\" className=\"block py-2 text-gray-700 hover:text-gray-900 font-medium\">\n                  Gallery\n                </a>\n                <a href=\"#amenities\" className=\"block py-2 text-gray-700 hover:text-gray-900 font-medium\">\n                  Amenities\n                </a>\n                <a href=\"#faqs\" className=\"block py-2 text-gray-700 hover:text-gray-900 font-medium\">\n                  FAQs\n                </a>\n                <a href=\"#international\" className=\"block py-2 text-gray-700 hover:text-gray-900 font-medium\">\n                  International Students\n                </a>\n                <a href=\"#community\" className=\"block py-2 text-gray-700 hover:text-gray-900 font-medium\">\n                  Community Central\n                </a>\n                <a href=\"#neighborhood\" className=\"block py-2 text-gray-700 hover:text-gray-900 font-medium\">\n                  Neighborhood\n                </a>\n                <a href=\"#contact\" className=\"block py-2 text-gray-700 hover:text-gray-900 font-medium\">\n                  Contact Us\n                </a>\n                <a href=\"#residents\" className=\"block py-2 text-gray-700 hover:text-gray-900 font-medium\">\n                  Residents\n                </a>\n                <button className=\"w-full mt-4 bg-red-600 text-white px-6 py-2 rounded-md font-medium hover:bg-red-700 transition-colors\">\n                  Apply Now\n                </button>\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </header>\n    </>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAAA;AAJA;;;;;AAMA,MAAM,SAAS;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,MAAM,aAAa,IAAM,cAAc,CAAC;IACxC,MAAM,qBAAqB,IAAM,sBAAsB,CAAC;IAExD,qBACE;;0BAEE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAAsB;;;;;;;;;;;0BAMrC,8OAAC;gBAAO,WAAU;;kCAChB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDAAmC;;;;;;;;;;;8CAMpD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,MAAK;4CAAe,WAAU;sDAAgD;;;;;;sDAGjF,8OAAC;4CAAE,MAAK;4CAAW,WAAU;sDAAgD;;;;;;sDAG7E,8OAAC;4CAAE,MAAK;4CAAa,WAAU;sDAAgD;;;;;;sDAK/E,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS;oDACT,WAAU;;wDACX;sEAEC,8OAAC,oNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;;8DAGzB,8OAAC,yLAAA,CAAA,kBAAe;8DACb,oCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,SAAS;4DAAE,SAAS;4DAAG,GAAG,CAAC;wDAAG;wDAC9B,SAAS;4DAAE,SAAS;4DAAG,GAAG;wDAAE;wDAC5B,MAAM;4DAAE,SAAS;4DAAG,GAAG,CAAC;wDAAG;wDAC3B,WAAU;;0EAEV,8OAAC;gEAAE,MAAK;gEAAQ,WAAU;0EAA0D;;;;;;0EAGpF,8OAAC;gEAAE,MAAK;gEAAiB,WAAU;0EAA0D;;;;;;0EAG7F,8OAAC;gEAAE,MAAK;gEAAa,WAAU;0EAA0D;;;;;;;;;;;;;;;;;;;;;;;sDAQjG,8OAAC;4CAAE,MAAK;4CAAgB,WAAU;sDAAgD;;;;;;sDAGlF,8OAAC;4CAAE,MAAK;4CAAW,WAAU;sDAAgD;;;;;;sDAG7E,8OAAC;4CAAE,MAAK;4CAAa,WAAU;sDAAgD;;;;;;;;;;;;8CAMjF,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAO,WAAU;kDAA4F;;;;;;;;;;;8CAMhH,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,SAAS;wCACT,WAAU;kDAET,2BAAa,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;qGAAe,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOlE,8OAAC,yLAAA,CAAA,kBAAe;kCACb,4BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,QAAQ;4BAAE;4BACjC,SAAS;gCAAE,SAAS;gCAAG,QAAQ;4BAAO;4BACtC,MAAM;gCAAE,SAAS;gCAAG,QAAQ;4BAAE;4BAC9B,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,MAAK;wCAAe,WAAU;kDAA2D;;;;;;kDAG5F,8OAAC;wCAAE,MAAK;wCAAW,WAAU;kDAA2D;;;;;;kDAGxF,8OAAC;wCAAE,MAAK;wCAAa,WAAU;kDAA2D;;;;;;kDAG1F,8OAAC;wCAAE,MAAK;wCAAQ,WAAU;kDAA2D;;;;;;kDAGrF,8OAAC;wCAAE,MAAK;wCAAiB,WAAU;kDAA2D;;;;;;kDAG9F,8OAAC;wCAAE,MAAK;wCAAa,WAAU;kDAA2D;;;;;;kDAG1F,8OAAC;wCAAE,MAAK;wCAAgB,WAAU;kDAA2D;;;;;;kDAG7F,8OAAC;wCAAE,MAAK;wCAAW,WAAU;kDAA2D;;;;;;kDAGxF,8OAAC;wCAAE,MAAK;wCAAa,WAAU;kDAA2D;;;;;;kDAG1F,8OAAC;wCAAO,WAAU;kDAAwG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1I;uCAEe", "debugId": null}}, {"offset": {"line": 408, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/crimson-crossing/src/app/components/HeroSection.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\n\nconst HeroSection = () => {\n  return (\n    <section className=\"relative bg-gradient-to-r from-blue-900 to-blue-700 text-white py-20 px-4\">\n      {/* Background overlay */}\n      <div className=\"absolute inset-0 bg-black bg-opacity-30\"></div>\n      \n      {/* Content */}\n      <div className=\"relative max-w-7xl mx-auto text-center\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n        >\n          <h1 className=\"text-4xl md:text-6xl font-bold mb-6\">\n            IU Bloomington Off-Campus Housing\n          </h1>\n          \n          <p className=\"text-xl md:text-2xl mb-8 max-w-3xl mx-auto leading-relaxed\">\n            Welcome Hoosiers! Our modern student apartments near IU Bloomington are the perfect place to spend your college years.\n          </p>\n          \n          <motion.button\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n            className=\"bg-red-600 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-red-700 transition-colors shadow-lg\"\n          >\n            Apply Now\n          </motion.button>\n        </motion.div>\n      </div>\n      \n      {/* Decorative elements */}\n      <div className=\"absolute bottom-0 left-0 w-full h-20 bg-gradient-to-t from-white to-transparent\"></div>\n    </section>\n  );\n};\n\nexport default HeroSection;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,cAAc;IAClB,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAIpD,8OAAC;4BAAE,WAAU;sCAA6D;;;;;;sCAI1E,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;4BACxB,WAAU;sCACX;;;;;;;;;;;;;;;;;0BAOL,8OAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;uCAEe", "debugId": null}}, {"offset": {"line": 502, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/crimson-crossing/src/app/components/ElevatedLivingSection.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\n\nconst ElevatedLivingSection = () => {\n  return (\n    <section className=\"py-20 px-4 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto\">\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n          {/* Content */}\n          <motion.div\n            initial={{ opacity: 0, x: -30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-4\">\n              Elevated Student Living\n              <span className=\"block text-red-600\">Starts Here</span>\n            </h2>\n            \n            <p className=\"text-lg text-gray-700 mb-8 leading-relaxed\">\n              Combining comfort, convenience, and a touch of sophistication, <PERSON> Monroe redefines the off-campus housing near IU Bloomington experience. Here, students are situated only a short distance from grocery stores, restaurants, bus routes, and the Indiana University Bloomington campus. Residents also enjoy cozy study nooks, premier amenities, and furnished interiors, each equipped with chic stainless steel appliances and a full-size, in-unit washer and dryer.\n            </p>\n            \n            <p className=\"text-lg text-gray-700 mb-8 leading-relaxed\">\n              Discover a community specially designed for your needs as a student. Schedule an in-person tour today for a closer look at The Monroe!\n            </p>\n            \n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              className=\"bg-red-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-red-700 transition-colors\"\n            >\n              Schedule Tour\n            </motion.button>\n          </motion.div>\n          \n          {/* Image Gallery */}\n          <motion.div\n            initial={{ opacity: 0, x: 30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"grid grid-cols-2 gap-4\"\n          >\n            <div className=\"space-y-4\">\n              <div className=\"bg-gray-300 h-48 rounded-lg flex items-center justify-center\">\n                <span className=\"text-gray-600\">Apartment Interior</span>\n              </div>\n              <div className=\"bg-gray-300 h-32 rounded-lg flex items-center justify-center\">\n                <span className=\"text-gray-600\">Kitchen</span>\n              </div>\n            </div>\n            <div className=\"space-y-4 mt-8\">\n              <div className=\"bg-gray-300 h-32 rounded-lg flex items-center justify-center\">\n                <span className=\"text-gray-600\">Living Room</span>\n              </div>\n              <div className=\"bg-gray-300 h-48 rounded-lg flex items-center justify-center\">\n                <span className=\"text-gray-600\">Bedroom</span>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default ElevatedLivingSection;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,wBAAwB;IAC5B,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;;0CAEvB,8OAAC;gCAAG,WAAU;;oCAAoD;kDAEhE,8OAAC;wCAAK,WAAU;kDAAqB;;;;;;;;;;;;0CAGvC,8OAAC;gCAAE,WAAU;0CAA6C;;;;;;0CAI1D,8OAAC;gCAAE,WAAU;0CAA6C;;;;;;0CAI1D,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAU;0CACX;;;;;;;;;;;;kCAMH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;kDAElC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;;;;;;;0CAGpC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;kDAElC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhD;uCAEe", "debugId": null}}, {"offset": {"line": 711, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/crimson-crossing/src/app/components/ScenicViewsSection.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\n\nconst ScenicViewsSection = () => {\n  return (\n    <section className=\"relative py-32 px-4 bg-cover bg-center bg-blue-900\">\n      {/* Background overlay */}\n      <div className=\"absolute inset-0 bg-black bg-opacity-50\"></div>\n      \n      {/* Content */}\n      <div className=\"relative max-w-7xl mx-auto text-center text-white\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n        >\n          <h2 className=\"text-4xl md:text-5xl font-bold mb-6\">\n            Scenic Views\n          </h2>\n          \n          <p className=\"text-xl md:text-2xl mb-8 max-w-3xl mx-auto leading-relaxed\">\n            Our Bloomington apartments overlook tranquil ponds, which sparkle under the warm Indiana sun.\n          </p>\n          \n          <motion.button\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n            className=\"bg-white text-blue-900 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors\"\n          >\n            All Amenities\n          </motion.button>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default ScenicViewsSection;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,qBAAqB;IACzB,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;;sCAEvB,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAIpD,8OAAC;4BAAE,WAAU;sCAA6D;;;;;;sCAI1E,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;4BACxB,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX;uCAEe", "debugId": null}}, {"offset": {"line": 801, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/crimson-crossing/src/app/components/FloorPlansSection.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\n\nconst FloorPlansSection = () => {\n  const floorPlans = [\n    {\n      bedrooms: '2 Bedroom',\n      sqft: '784–800 sqft',\n      price: 'Starting at $960',\n      image: 'bg-gray-300'\n    },\n    {\n      bedrooms: '4 Bedroom',\n      sqft: '1240 sqft',\n      price: 'Starting at $840',\n      image: 'bg-gray-300'\n    }\n  ];\n\n  return (\n    <section id=\"floor-plans\" className=\"py-20 px-4 bg-white\">\n      <div className=\"max-w-7xl mx-auto\">\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n          {/* Content */}\n          <motion.div\n            initial={{ opacity: 0, x: -30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-4\">\n              Spacious Floor Plans\n            </h2>\n            \n            <h3 className=\"text-2xl font-semibold text-red-600 mb-6\">\n              2- & 4-Bedroom Apartments\n            </h3>\n            \n            <p className=\"text-lg text-gray-700 mb-8 leading-relaxed\">\n              Whether you want to live with one friend or three, The Monroe has the perfect living arrangement for you! Our two-bedroom and four-bedroom units are ready for you to create unforgettable memories. Contact us today about our furnished apartments near Indiana University Bloomington!\n            </p>\n            \n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              className=\"bg-red-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-red-700 transition-colors\"\n            >\n              All Floor Plans\n            </motion.button>\n          </motion.div>\n          \n          {/* Floor Plan Cards */}\n          <motion.div\n            initial={{ opacity: 0, x: 30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"space-y-6\"\n          >\n            {floorPlans.map((plan, index) => (\n              <motion.div\n                key={index}\n                whileHover={{ scale: 1.02 }}\n                className=\"bg-white rounded-lg shadow-lg overflow-hidden border\"\n              >\n                <div className=\"flex\">\n                  <div className={`w-32 h-32 ${plan.image} flex items-center justify-center`}>\n                    <span className=\"text-gray-600 text-sm\">Floor Plan</span>\n                  </div>\n                  <div className=\"flex-1 p-6\">\n                    <h4 className=\"text-xl font-bold text-gray-900 mb-2\">\n                      {plan.bedrooms}\n                    </h4>\n                    <p className=\"text-gray-600 mb-1\">{plan.sqft}</p>\n                    <p className=\"text-red-600 font-semibold mb-3\">{plan.price}</p>\n                    <button className=\"text-red-600 font-medium hover:text-red-700\">\n                      View All\n                    </button>\n                  </div>\n                </div>\n              </motion.div>\n            ))}\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default FloorPlansSection;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,oBAAoB;IACxB,MAAM,aAAa;QACjB;YACE,UAAU;YACV,MAAM;YACN,OAAO;YACP,OAAO;QACT;QACA;YACE,UAAU;YACV,MAAM;YACN,OAAO;YACP,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAc,WAAU;kBAClC,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;;0CAEvB,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAIlE,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAIzD,8OAAC;gCAAE,WAAU;0CAA6C;;;;;;0CAI1D,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAU;0CACX;;;;;;;;;;;;kCAMH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAET,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAW,CAAC,UAAU,EAAE,KAAK,KAAK,CAAC,iCAAiC,CAAC;sDACxE,cAAA,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;sDAE1C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DACX,KAAK,QAAQ;;;;;;8DAEhB,8OAAC;oDAAE,WAAU;8DAAsB,KAAK,IAAI;;;;;;8DAC5C,8OAAC;oDAAE,WAAU;8DAAmC,KAAK,KAAK;;;;;;8DAC1D,8OAAC;oDAAO,WAAU;8DAA8C;;;;;;;;;;;;;;;;;;+BAd/D;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BrB;uCAEe", "debugId": null}}, {"offset": {"line": 1010, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/crimson-crossing/src/app/components/AmenitiesSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\nconst AmenitiesSection = () => {\n  const [activeTab, setActiveTab] = useState(0);\n\n  const amenities = [\n    {\n      title: 'Private Study Rooms',\n      description: '24-Hour Study Center',\n      content: 'Our study nooks and private study rooms are perfect for group projects, homework assignments, and pre-exam study sessions.',\n      image: 'bg-blue-200'\n    },\n    {\n      title: 'Outdoor Swimming Pool',\n      description: 'Resort-Style Swimming Pool',\n      content: 'Our outdoor swimming pool is a rejuvenating escape from the summer heat.',\n      image: 'bg-blue-400'\n    },\n    {\n      title: '24/7 Fitness Hub',\n      description: '24/7 Fitness Hub',\n      content: 'Our fully equipped fitness center contains free weights, strength training equipment, a yoga room, and Fitness On Demand.',\n      image: 'bg-green-200'\n    },\n    {\n      title: 'Basketball & Volleyball Courts',\n      description: 'Basketball & Volleyball Courts',\n      content: 'Challenge your friends to a friendly competition at our outdoor basketball and sand volleyball courts.',\n      image: 'bg-orange-200'\n    },\n    {\n      title: 'Package Lockers',\n      description: 'Luxer One Package Lockers',\n      content: 'Deliveries have never been so simple than with our community package lockers.',\n      image: 'bg-purple-200'\n    }\n  ];\n\n  return (\n    <section id=\"amenities\" className=\"py-20 px-4 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto\">\n        {/* Tab Navigation */}\n        <div className=\"flex flex-wrap justify-center mb-12 space-x-2 space-y-2 md:space-y-0\">\n          {amenities.map((amenity, index) => (\n            <button\n              key={index}\n              onClick={() => setActiveTab(index)}\n              className={`px-6 py-3 rounded-lg font-medium transition-colors ${\n                activeTab === index\n                  ? 'bg-red-600 text-white'\n                  : 'bg-white text-gray-700 hover:bg-gray-100'\n              }`}\n            >\n              {amenity.title}\n            </button>\n          ))}\n        </div>\n\n        {/* Content */}\n        <AnimatePresence mode=\"wait\">\n          <motion.div\n            key={activeTab}\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            exit={{ opacity: 0, y: -20 }}\n            transition={{ duration: 0.5 }}\n            className=\"grid lg:grid-cols-2 gap-12 items-center\"\n          >\n            {/* Image */}\n            <div className={`h-96 rounded-lg ${amenities[activeTab].image} flex items-center justify-center`}>\n              <span className=\"text-gray-700 text-lg\">{amenities[activeTab].title}</span>\n            </div>\n\n            {/* Content */}\n            <div>\n              <h3 className=\"text-3xl font-bold text-gray-900 mb-4\">\n                {amenities[activeTab].description}\n              </h3>\n              \n              <p className=\"text-lg text-gray-700 mb-8 leading-relaxed\">\n                {amenities[activeTab].content}\n              </p>\n              \n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"bg-red-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-red-700 transition-colors\"\n              >\n                All Amenities\n              </motion.button>\n            </div>\n          </motion.div>\n        </AnimatePresence>\n      </div>\n    </section>\n  );\n};\n\nexport default AmenitiesSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAKA,MAAM,mBAAmB;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,YAAY;QAChB;YACE,OAAO;YACP,aAAa;YACb,SAAS;YACT,OAAO;QACT;QACA;YACE,OAAO;YACP,aAAa;YACb,SAAS;YACT,OAAO;QACT;QACA;YACE,OAAO;YACP,aAAa;YACb,SAAS;YACT,OAAO;QACT;QACA;YACE,OAAO;YACP,aAAa;YACb,SAAS;YACT,OAAO;QACT;QACA;YACE,OAAO;YACP,aAAa;YACb,SAAS;YACT,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAY,WAAU;kBAChC,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACZ,UAAU,GAAG,CAAC,CAAC,SAAS,sBACvB,8OAAC;4BAEC,SAAS,IAAM,aAAa;4BAC5B,WAAW,CAAC,mDAAmD,EAC7D,cAAc,QACV,0BACA,4CACJ;sCAED,QAAQ,KAAK;2BART;;;;;;;;;;8BAcX,8OAAC,yLAAA,CAAA,kBAAe;oBAAC,MAAK;8BACpB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,MAAM;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC3B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;;0CAGV,8OAAC;gCAAI,WAAW,CAAC,gBAAgB,EAAE,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,iCAAiC,CAAC;0CAC9F,cAAA,8OAAC;oCAAK,WAAU;8CAAyB,SAAS,CAAC,UAAU,CAAC,KAAK;;;;;;;;;;;0CAIrE,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDACX,SAAS,CAAC,UAAU,CAAC,WAAW;;;;;;kDAGnC,8OAAC;wCAAE,WAAU;kDACV,SAAS,CAAC,UAAU,CAAC,OAAO;;;;;;kDAG/B,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;kDACX;;;;;;;;;;;;;uBA1BE;;;;;;;;;;;;;;;;;;;;;AAmCjB;uCAEe", "debugId": null}}, {"offset": {"line": 1178, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/crimson-crossing/src/app/components/PolishedInteriorsSection.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\n\nconst PolishedInteriorsSection = () => {\n  return (\n    <section className=\"py-20 px-4 bg-white\">\n      <div className=\"max-w-7xl mx-auto\">\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n          {/* Image Gallery */}\n          <motion.div\n            initial={{ opacity: 0, x: -30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"grid grid-cols-2 gap-4\"\n          >\n            <div className=\"space-y-4\">\n              <div className=\"bg-gray-300 h-48 rounded-lg flex items-center justify-center\">\n                <span className=\"text-gray-600\">Modern Kitchen</span>\n              </div>\n              <div className=\"bg-gray-300 h-32 rounded-lg flex items-center justify-center\">\n                <span className=\"text-gray-600\">Bathroom</span>\n              </div>\n            </div>\n            <div className=\"space-y-4 mt-8\">\n              <div className=\"bg-gray-300 h-32 rounded-lg flex items-center justify-center\">\n                <span className=\"text-gray-600\">Living Space</span>\n              </div>\n              <div className=\"bg-gray-300 h-48 rounded-lg flex items-center justify-center\">\n                <span className=\"text-gray-600\">Bedroom</span>\n              </div>\n            </div>\n          </motion.div>\n          \n          {/* Content */}\n          <motion.div\n            initial={{ opacity: 0, x: 30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-4\">\n              Polished & Streamlined Interiors\n            </h2>\n            \n            <h3 className=\"text-2xl font-semibold text-red-600 mb-6\">\n              With Refined Finishes\n            </h3>\n            \n            <p className=\"text-lg text-gray-700 mb-6 leading-relaxed\">\n              Student apartments are more than a place to live. They host late-night study sessions, roommate movie nights, and serve as the backdrop for some of the most defining years of your life. You deserve a space that can keep up with it all.\n            </p>\n            \n            <p className=\"text-lg text-gray-700 mb-8 leading-relaxed\">\n              At The Monroe, students can call our luxurious student apartments near IU Bloomington home. Featuring wood-plank style flooring, large windows, and elegant gray cabinetry, these stunning units are designed to impress.\n            </p>\n            \n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              className=\"bg-red-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-red-700 transition-colors\"\n            >\n              Schedule Tour\n            </motion.button>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default PolishedInteriorsSection;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,2BAA2B;IAC/B,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;kDAElC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;;;;;;;0CAGpC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;kDAElC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;kCAMtC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;;0CAEvB,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAIlE,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAIzD,8OAAC;gCAAE,WAAU;0CAA6C;;;;;;0CAI1D,8OAAC;gCAAE,WAAU;0CAA6C;;;;;;0CAI1D,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;uCAEe", "debugId": null}}, {"offset": {"line": 1385, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/crimson-crossing/src/app/components/NeighborhoodSection.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\n\nconst NeighborhoodSection = () => {\n  const locations = [\n    {\n      name: 'Indiana University Bloomington',\n      distance: '3.4 Miles Away',\n      icon: '🎓'\n    },\n    {\n      name: '<PERSON><PERSON><PERSON>',\n      distance: '0.5 Miles Away',\n      icon: '🛒'\n    },\n    {\n      name: \"<PERSON> & Buster's\",\n      distance: '1.1 Miles Away',\n      icon: '🎮'\n    }\n  ];\n\n  return (\n    <section id=\"neighborhood\" className=\"relative py-32 px-4 bg-cover bg-center bg-green-900\">\n      {/* Background overlay */}\n      <div className=\"absolute inset-0 bg-black bg-opacity-60\"></div>\n      \n      {/* Content */}\n      <div className=\"relative max-w-7xl mx-auto\">\n        <div className=\"text-center text-white mb-16\">\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <h2 className=\"text-4xl md:text-5xl font-bold mb-6\">\n              Stay Connected To Campus!\n            </h2>\n            \n            <p className=\"text-xl md:text-2xl mb-8 max-w-4xl mx-auto leading-relaxed\">\n              When you live off campus, you can experience the convenience of short commutes to libraries, lectures, and social events, while also having the flexibility and freedom to explore everything else that Bloomington has to offer.\n            </p>\n            \n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              className=\"bg-white text-green-900 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors\"\n            >\n              Start Exploring\n            </motion.button>\n          </motion.div>\n        </div>\n\n        {/* Location Cards */}\n        <div className=\"grid md:grid-cols-3 gap-8\">\n          {locations.map((location, index) => (\n            <motion.div\n              key={index}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: index * 0.2 }}\n              viewport={{ once: true }}\n              className=\"bg-white bg-opacity-90 rounded-lg p-6 text-center\"\n            >\n              <div className=\"text-4xl mb-4\">{location.icon}</div>\n              <h3 className=\"text-xl font-bold text-gray-900 mb-2\">\n                {location.name}\n              </h3>\n              <p className=\"text-gray-700 font-medium\">\n                {location.distance}\n              </p>\n            </motion.div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default NeighborhoodSection;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,sBAAsB;IAC1B,MAAM,YAAY;QAChB;YACE,MAAM;YACN,UAAU;YACV,MAAM;QACR;QACA;YACE,MAAM;YACN,UAAU;YACV,MAAM;QACR;QACA;YACE,MAAM;YACN,UAAU;YACV,MAAM;QACR;KACD;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAe,WAAU;;0BAEnC,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAIpD,8OAAC;oCAAE,WAAU;8CAA6D;;;;;;8CAI1E,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;8CACX;;;;;;;;;;;;;;;;;kCAOL,8OAAC;wBAAI,WAAU;kCACZ,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;gCAChD,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;kDAAiB,SAAS,IAAI;;;;;;kDAC7C,8OAAC;wCAAG,WAAU;kDACX,SAAS,IAAI;;;;;;kDAEhB,8OAAC;wCAAE,WAAU;kDACV,SAAS,QAAQ;;;;;;;+BAZf;;;;;;;;;;;;;;;;;;;;;;AAoBnB;uCAEe", "debugId": null}}, {"offset": {"line": 1557, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/crimson-crossing/src/app/components/ContactSection.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { Phone, Clock, MapPin } from 'lucide-react';\n\nconst ContactSection = () => {\n  return (\n    <section id=\"contact\" className=\"py-20 px-4 bg-gray-900 text-white\">\n      <div className=\"max-w-7xl mx-auto\">\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n          {/* Contact Info */}\n          <motion.div\n            initial={{ opacity: 0, x: -30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <h2 className=\"text-3xl font-bold mb-8\">Get In Touch</h2>\n            \n            <div className=\"space-y-6\">\n              <div className=\"flex items-center space-x-4\">\n                <Phone className=\"h-6 w-6 text-red-500\" />\n                <div>\n                  <p className=\"font-semibold\">Call: (*************</p>\n                  <p className=\"text-gray-300\">Text: (*************</p>\n                </div>\n              </div>\n              \n              <div className=\"flex items-start space-x-4\">\n                <Clock className=\"h-6 w-6 text-red-500 mt-1\" />\n                <div>\n                  <p className=\"font-semibold mb-2\">Office Hours</p>\n                  <div className=\"text-gray-300 space-y-1\">\n                    <p>Mon-Fri: 9AM – 6PM</p>\n                    <p>Sat: 10AM – 4PM</p>\n                    <p>Sun: 12PM – 4PM</p>\n                  </div>\n                </div>\n              </div>\n              \n              <div className=\"flex items-start space-x-4\">\n                <MapPin className=\"h-6 w-6 text-red-500 mt-1\" />\n                <div>\n                  <p className=\"font-semibold\">Address</p>\n                  <p className=\"text-gray-300\">\n                    1150 S Clarizz Blvd<br />\n                    Bloomington, IN 47401\n                  </p>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n          \n          {/* Map Placeholder */}\n          <motion.div\n            initial={{ opacity: 0, x: 30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"bg-gray-700 h-96 rounded-lg flex items-center justify-center\"\n          >\n            <span className=\"text-gray-400\">Interactive Map</span>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default ContactSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,iBAAiB;IACrB,qBACE,8OAAC;QAAQ,IAAG;QAAU,WAAU;kBAC9B,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;;0CAEvB,8OAAC;gCAAG,WAAU;0CAA0B;;;;;;0CAExC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAgB;;;;;;kEAC7B,8OAAC;wDAAE,WAAU;kEAAgB;;;;;;;;;;;;;;;;;;kDAIjC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAqB;;;;;;kEAClC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAE;;;;;;0EACH,8OAAC;0EAAE;;;;;;0EACH,8OAAC;0EAAE;;;;;;;;;;;;;;;;;;;;;;;;kDAKT,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAgB;;;;;;kEAC7B,8OAAC;wDAAE,WAAU;;4DAAgB;0EACR,8OAAC;;;;;4DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASnC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAEV,cAAA,8OAAC;4BAAK,WAAU;sCAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5C;uCAEe", "debugId": null}}, {"offset": {"line": 1818, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/crimson-crossing/src/app/components/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport { Facebook, Instagram, Twitter, Youtube } from 'lucide-react';\n\nconst Footer = () => {\n  return (\n    <footer className=\"bg-black text-white\">\n      {/* Main Footer */}\n      <div className=\"max-w-7xl mx-auto px-4 py-12\">\n        <div className=\"grid md:grid-cols-4 gap-8\">\n          {/* Logo and Address */}\n          <div className=\"md:col-span-2\">\n            <div className=\"text-2xl font-bold mb-4\">The Monroe</div>\n            <p className=\"text-gray-300 mb-4\">\n              1150 S Clarizz Blvd<br />\n              Bloomington, IN 47401\n            </p>\n          </div>\n          \n          {/* Quick Links */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Quick Links</h3>\n            <ul className=\"space-y-2 text-gray-300\">\n              <li><a href=\"#residents\" className=\"hover:text-white\">Resident Login</a></li>\n              <li><a href=\"#apply\" className=\"hover:text-white\">Apply Now</a></li>\n              <li><a href=\"#contact\" className=\"hover:text-white\">Contact Us</a></li>\n              <li><a href=\"#tour\" className=\"hover:text-white\">Schedule A Tour</a></li>\n            </ul>\n          </div>\n          \n          {/* Social Media */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Follow Us</h3>\n            <div className=\"flex space-x-4\">\n              <a href=\"#\" className=\"text-gray-300 hover:text-white\">\n                <Facebook className=\"h-6 w-6\" />\n              </a>\n              <a href=\"#\" className=\"text-gray-300 hover:text-white\">\n                <Instagram className=\"h-6 w-6\" />\n              </a>\n              <a href=\"#\" className=\"text-gray-300 hover:text-white\">\n                <Twitter className=\"h-6 w-6\" />\n              </a>\n              <a href=\"#\" className=\"text-gray-300 hover:text-white\">\n                <Youtube className=\"h-6 w-6\" />\n              </a>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      {/* Bottom Footer */}\n      <div className=\"border-t border-gray-800\">\n        <div className=\"max-w-7xl mx-auto px-4 py-6\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <p className=\"text-gray-300 text-sm\">\n              © 2025 The Monroe. All Rights Reserved.\n            </p>\n            <div className=\"flex space-x-6 mt-4 md:mt-0\">\n              <a href=\"#\" className=\"text-gray-300 hover:text-white text-sm\">Privacy Policy</a>\n              <a href=\"#\" className=\"text-gray-300 hover:text-white text-sm\">Terms Of Use</a>\n              <a href=\"#\" className=\"text-gray-300 hover:text-white text-sm\">Accessibility</a>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAFA;;;AAIA,MAAM,SAAS;IACb,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAA0B;;;;;;8CACzC,8OAAC;oCAAE,WAAU;;wCAAqB;sDACb,8OAAC;;;;;wCAAK;;;;;;;;;;;;;sCAM7B,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC;gDAAE,MAAK;gDAAa,WAAU;0DAAmB;;;;;;;;;;;sDACtD,8OAAC;sDAAG,cAAA,8OAAC;gDAAE,MAAK;gDAAS,WAAU;0DAAmB;;;;;;;;;;;sDAClD,8OAAC;sDAAG,cAAA,8OAAC;gDAAE,MAAK;gDAAW,WAAU;0DAAmB;;;;;;;;;;;sDACpD,8OAAC;sDAAG,cAAA,8OAAC;gDAAE,MAAK;gDAAQ,WAAU;0DAAmB;;;;;;;;;;;;;;;;;;;;;;;sCAKrD,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,MAAK;4CAAI,WAAU;sDACpB,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,8OAAC;4CAAE,MAAK;4CAAI,WAAU;sDACpB,cAAA,8OAAC,4MAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAEvB,8OAAC;4CAAE,MAAK;4CAAI,WAAU;sDACpB,cAAA,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;sDAErB,8OAAC;4CAAE,MAAK;4CAAI,WAAU;sDACpB,cAAA,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ7B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;0CAGrC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,MAAK;wCAAI,WAAU;kDAAyC;;;;;;kDAC/D,8OAAC;wCAAE,MAAK;wCAAI,WAAU;kDAAyC;;;;;;kDAC/D,8OAAC;wCAAE,MAAK;wCAAI,WAAU;kDAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7E;uCAEe", "debugId": null}}, {"offset": {"line": 2131, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/crimson-crossing/src/app/components/SpecialOfferPopup.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { X } from 'lucide-react';\n\nconst SpecialOfferPopup = () => {\n  const [isOpen, setIsOpen] = useState(false);\n\n  useEffect(() => {\n    // Show popup after 3 seconds\n    const timer = setTimeout(() => {\n      setIsOpen(true);\n    }, 3000);\n\n    return () => clearTimeout(timer);\n  }, []);\n\n  const closePopup = () => {\n    setIsOpen(false);\n  };\n\n  return (\n    <AnimatePresence>\n      {isOpen && (\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          exit={{ opacity: 0 }}\n          className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\"\n          onClick={closePopup}\n        >\n          <motion.div\n            initial={{ scale: 0.8, opacity: 0 }}\n            animate={{ scale: 1, opacity: 1 }}\n            exit={{ scale: 0.8, opacity: 0 }}\n            className=\"bg-white rounded-lg max-w-md w-full p-6 relative\"\n            onClick={(e) => e.stopPropagation()}\n          >\n            {/* Close Button */}\n            <button\n              onClick={closePopup}\n              className=\"absolute top-4 right-4 text-gray-500 hover:text-gray-700\"\n            >\n              <X className=\"h-6 w-6\" />\n            </button>\n\n            {/* Content */}\n            <div className=\"text-center\">\n              <div className=\"bg-red-600 text-white px-4 py-2 rounded-lg inline-block mb-4\">\n                <span className=\"text-sm font-semibold\">Special Offer</span>\n              </div>\n              \n              <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n                Freedom feels like $1,700 back in your pocket\n              </h3>\n              \n              <div className=\"bg-yellow-100 border border-yellow-300 rounded-lg p-4 mb-6\">\n                <h4 className=\"text-lg font-bold text-gray-900 mb-2\">\n                  $1,700 Lease Drop – This Weekend Only\n                </h4>\n                <ul className=\"text-left text-gray-700 space-y-1\">\n                  <li>🔥 $1,700 Gift Card at Move-In</li>\n                  <li>💸 Effective Rate: $699 for 4x2s</li>\n                  <li>Note: Utilities NOT included</li>\n                </ul>\n              </div>\n              \n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"w-full bg-red-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-red-700 transition-colors\"\n                onClick={closePopup}\n              >\n                Apply Now\n              </motion.button>\n            </div>\n          </motion.div>\n        </motion.div>\n      )}\n    </AnimatePresence>\n  );\n};\n\nexport default SpecialOfferPopup;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAMA,MAAM,oBAAoB;IACxB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,6BAA6B;QAC7B,MAAM,QAAQ,WAAW;YACvB,UAAU;QACZ,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG,EAAE;IAEL,MAAM,aAAa;QACjB,UAAU;IACZ;IAEA,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,wBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;YACnB,WAAU;YACV,SAAS;sBAET,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,OAAO;oBAAK,SAAS;gBAAE;gBAClC,SAAS;oBAAE,OAAO;oBAAG,SAAS;gBAAE;gBAChC,MAAM;oBAAE,OAAO;oBAAK,SAAS;gBAAE;gBAC/B,WAAU;gBACV,SAAS,CAAC,IAAM,EAAE,eAAe;;kCAGjC,8OAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;kCAIf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAwB;;;;;;;;;;;0CAG1C,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAItD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAuC;;;;;;kDAGrD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAIR,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAU;gCACV,SAAS;0CACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;uCAEe", "debugId": null}}]}