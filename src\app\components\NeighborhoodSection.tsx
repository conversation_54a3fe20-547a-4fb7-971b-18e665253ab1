'use client';

import { motion } from 'framer-motion';

const NeighborhoodSection = () => {
  const locations = [
    {
      name: 'Indiana University Bloomington',
      distance: '3.4 Miles Away',
      icon: '🎓'
    },
    {
      name: '<PERSON><PERSON><PERSON>',
      distance: '0.5 Miles Away',
      icon: '🛒'
    },
    {
      name: "<PERSON> & Buster's",
      distance: '1.1 Miles Away',
      icon: '🎮'
    }
  ];

  return (
    <section id="neighborhood" className="relative py-32 px-4 bg-cover bg-center bg-green-900">
      {/* Background overlay */}
      <div className="absolute inset-0 bg-black bg-opacity-60"></div>
      
      {/* Content */}
      <div className="relative max-w-7xl mx-auto">
        <div className="text-center text-white mb-16">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              Stay Connected To Campus!
            </h2>
            
            <p className="text-xl md:text-2xl mb-8 max-w-4xl mx-auto leading-relaxed">
              When you live off campus, you can experience the convenience of short commutes to libraries, lectures, and social events, while also having the flexibility and freedom to explore everything else that Bloomington has to offer.
            </p>
            
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-white text-green-900 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
            >
              Start Exploring
            </motion.button>
          </motion.div>
        </div>

        {/* Location Cards */}
        <div className="grid md:grid-cols-3 gap-8">
          {locations.map((location, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              viewport={{ once: true }}
              className="bg-white bg-opacity-90 rounded-lg p-6 text-center"
            >
              <div className="text-4xl mb-4">{location.icon}</div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">
                {location.name}
              </h3>
              <p className="text-gray-700 font-medium">
                {location.distance}
              </p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default NeighborhoodSection;
