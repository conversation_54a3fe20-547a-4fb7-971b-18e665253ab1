'use client';

import { motion } from 'framer-motion';

const FloorPlansSection = () => {
  const floorPlans = [
    {
      bedrooms: '2 Bedroom',
      sqft: '784–800 sqft',
      price: 'Starting at $960',
      image: 'bg-gray-300'
    },
    {
      bedrooms: '4 Bedroom',
      sqft: '1240 sqft',
      price: 'Starting at $840',
      image: 'bg-gray-300'
    }
  ];

  return (
    <section id="floor-plans" className="py-20 px-4 bg-white">
      <div className="max-w-7xl mx-auto">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Spacious Floor Plans
            </h2>
            
            <h3 className="text-2xl font-semibold text-red-600 mb-6">
              2- & 4-Bedroom Apartments
            </h3>
            
            <p className="text-lg text-gray-700 mb-8 leading-relaxed">
              Whether you want to live with one friend or three, The Monroe has the perfect living arrangement for you! Our two-bedroom and four-bedroom units are ready for you to create unforgettable memories. Contact us today about our furnished apartments near Indiana University Bloomington!
            </p>
            
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-red-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-red-700 transition-colors"
            >
              All Floor Plans
            </motion.button>
          </motion.div>
          
          {/* Floor Plan Cards */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            {floorPlans.map((plan, index) => (
              <motion.div
                key={index}
                whileHover={{ scale: 1.02 }}
                className="bg-white rounded-lg shadow-lg overflow-hidden border"
              >
                <div className="flex">
                  <div className={`w-32 h-32 ${plan.image} flex items-center justify-center`}>
                    <span className="text-gray-600 text-sm">Floor Plan</span>
                  </div>
                  <div className="flex-1 p-6">
                    <h4 className="text-xl font-bold text-gray-900 mb-2">
                      {plan.bedrooms}
                    </h4>
                    <p className="text-gray-600 mb-1">{plan.sqft}</p>
                    <p className="text-red-600 font-semibold mb-3">{plan.price}</p>
                    <button className="text-red-600 font-medium hover:text-red-700">
                      View All
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default FloorPlansSection;
