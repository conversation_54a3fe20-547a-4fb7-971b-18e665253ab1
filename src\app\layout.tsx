import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, Roboto_Slab } from "next/font/google";
import "./globals.css";

const poppins = Poppins({
  variable: "--font-poppins",
  subsets: ["latin"],
  weight: ["400", "600", "700"],
});

const robotoSlab = Roboto_Slab({
  variable: "--font-roboto-slab",
  subsets: ["latin"],
  weight: ["700"],
});

export const metadata: Metadata = {
  title: "Apartments Bloomington, Indiana - Crimson Crossing Apartments",
  description: "We offer 3-4 bedroom apartments near Indiana University, IU Stadium, & downtown Bloomington.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${poppins.variable} ${robotoSlab.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
