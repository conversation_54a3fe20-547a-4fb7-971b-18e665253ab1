'use client';

import { motion } from 'framer-motion';
import { Phone, Clock, MapPin } from 'lucide-react';

const ContactSection = () => {
  return (
    <section id="contact" className="py-20 px-4 bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Contact Info */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-8">Get In Touch</h2>
            
            <div className="space-y-6">
              <div className="flex items-center space-x-4">
                <Phone className="h-6 w-6 text-red-500" />
                <div>
                  <p className="font-semibold">Call: (*************</p>
                  <p className="text-gray-300">Text: (*************</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-4">
                <Clock className="h-6 w-6 text-red-500 mt-1" />
                <div>
                  <p className="font-semibold mb-2">Office Hours</p>
                  <div className="text-gray-300 space-y-1">
                    <p>Mon-Fri: 9AM – 6PM</p>
                    <p>Sat: 10AM – 4PM</p>
                    <p>Sun: 12PM – 4PM</p>
                  </div>
                </div>
              </div>
              
              <div className="flex items-start space-x-4">
                <MapPin className="h-6 w-6 text-red-500 mt-1" />
                <div>
                  <p className="font-semibold">Address</p>
                  <p className="text-gray-300">
                    1150 S Clarizz Blvd<br />
                    Bloomington, IN 47401
                  </p>
                </div>
              </div>
            </div>
          </motion.div>
          
          {/* Map Placeholder */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="bg-gray-700 h-96 rounded-lg flex items-center justify-center"
          >
            <span className="text-gray-400">Interactive Map</span>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;
