'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X } from 'lucide-react';

const SpecialOfferPopup = () => {
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    // Show popup after 3 seconds
    const timer = setTimeout(() => {
      setIsOpen(true);
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  const closePopup = () => {
    setIsOpen(false);
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          onClick={closePopup}
        >
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.8, opacity: 0 }}
            className="bg-white rounded-lg max-w-md w-full p-6 relative"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Close Button */}
            <button
              onClick={closePopup}
              className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
            >
              <X className="h-6 w-6" />
            </button>

            {/* Content */}
            <div className="text-center">
              <div className="bg-red-600 text-white px-4 py-2 rounded-lg inline-block mb-4">
                <span className="text-sm font-semibold">Special Offer</span>
              </div>
              
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                Freedom feels like $1,700 back in your pocket
              </h3>
              
              <div className="bg-yellow-100 border border-yellow-300 rounded-lg p-4 mb-6">
                <h4 className="text-lg font-bold text-gray-900 mb-2">
                  $1,700 Lease Drop – This Weekend Only
                </h4>
                <ul className="text-left text-gray-700 space-y-1">
                  <li>🔥 $1,700 Gift Card at Move-In</li>
                  <li>💸 Effective Rate: $699 for 4x2s</li>
                  <li>Note: Utilities NOT included</li>
                </ul>
              </div>
              
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="w-full bg-red-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-red-700 transition-colors"
                onClick={closePopup}
              >
                Apply Now
              </motion.button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default SpecialOfferPopup;
