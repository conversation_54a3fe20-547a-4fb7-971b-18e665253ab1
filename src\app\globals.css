@import "tailwindcss";

:root {
  /* Crimson Crossing Color Palette */
  --crimson-primary: #991d20;
  --crimson-secondary: #bc2426;
  --crimson-dark: #414042;
  --crimson-medium: #525053;
  --crimson-light: #5c5a5e;
  --crimson-gray: #6d6e71;
  --crimson-light-gray: #eaeaeb;
  --crimson-lighter-gray: #f1f1f2;
  --crimson-white: #ffffff;

  --background: var(--crimson-lighter-gray);
  --foreground: var(--crimson-medium);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-poppins);
  --font-mono: var(--font-poppins);

  /* Crimson Crossing Colors for Tailwind */
  --color-crimson-primary: var(--crimson-primary);
  --color-crimson-secondary: var(--crimson-secondary);
  --color-crimson-dark: var(--crimson-dark);
  --color-crimson-medium: var(--crimson-medium);
  --color-crimson-light: var(--crimson-light);
  --color-crimson-gray: var(--crimson-gray);
  --color-crimson-light-gray: var(--crimson-light-gray);
  --color-crimson-lighter-gray: var(--crimson-lighter-gray);
  --color-crimson-white: var(--crimson-white);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-poppins), sans-serif;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-roboto-slab), serif;
}
