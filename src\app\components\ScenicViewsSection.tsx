'use client';

import { motion } from 'framer-motion';

const ScenicViewsSection = () => {
  return (
    <section className="relative py-32 px-4 bg-cover bg-center bg-blue-900">
      {/* Background overlay */}
      <div className="absolute inset-0 bg-black bg-opacity-50"></div>
      
      {/* Content */}
      <div className="relative max-w-7xl mx-auto text-center text-white">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Scenic Views
          </h2>
          
          <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto leading-relaxed">
            Our Bloomington apartments overlook tranquil ponds, which sparkle under the warm Indiana sun.
          </p>
          
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="bg-white text-blue-900 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
          >
            All Amenities
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
};

export default ScenicViewsSection;
